'use client';

import { Button } from '@/components/ui/button';
import { usePmaManagementTranslations } from '@/hooks/use-translations';
import { useRouter } from '@/i18n/navigation';
import { ArrowLeft, FilePlus2, Save, X } from 'lucide-react';
import { useState } from 'react';
import { usePmaManagement } from '../hooks/use-pma-management';
import { type PmaEntry, type PmaFormData } from '../types/pma-types';
import { PmaEntriesList } from './pma-entries-list';
import { PmaForm } from './pma-form';

export function AddPmaPage() {
  const t = usePmaManagementTranslations();
  const common = useCommonTranslations();
  const router = useRouter();
  const {
    entries,
    currentForm,
    addEntry,
    updateEntry,
    deleteEntry, 
    setCurrentForm,
    resetCurrentForm,
    saveDraft,
    submitAllEntries,
  } = usePmaManagement();

  const [editingEntry, setEditingEntry] = useState<PmaEntry | null>(null);

  const handleFormSubmit = (formData: PmaFormData) => {
    if (editingEntry) {
      updateEntry(editingEntry.id, formData);
      setEditingEntry(null);
    } else {
      addEntry(formData);
    }
    resetCurrentForm();
  };

  const handleEdit = (entry: PmaEntry) => {
    setEditingEntry(entry);
    setCurrentForm(entry);
  };

  const handleDelete = (id: string) => {
    deleteEntry(id);
    if (editingEntry?.id === id) {
      setEditingEntry(null);
      resetCurrentForm();
    }
  };

  const handleAddMore = () => {
    setEditingEntry(null);
    resetCurrentForm();
  };

  const handleCancel = () => {
    setEditingEntry(null);
    resetCurrentForm();
  };

  const handleBackToDashboard = () => {
    router.push('/pmas');
  };
  return (
    <div className="min-h-screen bg-gray-50/30">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="p-3 rounded-xl bg-primary/10">
              <FilePlus2 className="h-8 w-8 text-primary" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{t('title')}</h1>
              <p className="text-sm text-gray-600 mt-1">{t('subtitle')}</p>
            </div>
          </div>
          <div>
            <Button
              variant="outline"
              onClick={handleBackToDashboard}
              className="gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Dashboard
            </Button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-6">
        <div className="max-w-5xl mx-auto space-y-6">
          {/* PMA Form */}
          <PmaForm
            onSubmit={handleFormSubmit}
            initialData={editingEntry || currentForm}
          />

          {/* Added PMA Entries */}
          {entries.length > 0 && (
            <PmaEntriesList
              entries={entries}
              onEdit={handleEdit}
              onDelete={handleDelete}
              onAddMore={handleAddMore}
            />
          )}

          {/* Action Buttons */}
          <div className="flex items-center justify-between pt-6">
            <div className="flex gap-3">
              <Button variant="outline" onClick={saveDraft} className="gap-2">
                <Save className="h-4 w-4" />
                {t('actions.saveDraft')}
              </Button>
              <Button
                variant="outline"
                onClick={handleAddMore}
                className="gap-2"
              >
                {t('actions.addAnotherPma')}
              </Button>
            </div>

            <div className="flex gap-3">
              <Button
                variant="outline"
                onClick={handleCancel}
                className="gap-2"
              >
                <X className="h-4 w-4" />
                {t('actions.cancel')}
              </Button>
              <Button
                onClick={submitAllEntries}
                disabled={entries.length === 0}
                className="gap-2 bg-blue-600 hover:bg-blue-700"
              >
                {t('actions.submitPma')}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
